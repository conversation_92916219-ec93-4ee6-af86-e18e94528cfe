#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport

    #export("content"):
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Edit Client Information</h1>
            <a href="/client/#(client.id)" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Profile
            </a>
        </div>

        <div class="card shadow">
            <div class="card-body">
                <h2 class="card-title mb-4">#(client.name)</h2>
                
                <form method="POST" action="/client/#(client.id)/edit">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="#(client.email)" placeholder="Enter email address">
                                <div class="form-text">Optional - Used for communication and notifications</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="#(client.phone)" placeholder="Enter phone number">
                                <div class="form-text">Optional - Used for emergency contact</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="location" name="location" 
                                       value="#(client.location)" placeholder="Enter location (e.g., Downtown, North Side, etc.)">
                                <div class="form-text">Optional - General area or neighborhood for matching purposes</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="/client/#(client.id)" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    #endexport

    #export("footer"):
        #extend("shared/footer")
    #endexport
#endextend
