import Fluent

struct CreateTodo: AsyncMigration {
    func prepare(on database: any Database) async throws {
        try await database.schema("todos")
            .id()
            .field("title", .string, .required)
            .create()
    }

    func revert(on database: any Database) async throws {
        try await database.schema("todos").delete()
    }
}

struct CreateClient: AsyncMigration {
    func prepare(on database: any Database) async throws {
        try await database.schema("clients")
               .id()
               .field("name", .string, .required)
               .create()
    }

    func revert(on database: any FluentKit.Database) async throws {
        try await database.schema("clients").delete()
    }
}


struct CreateCaregiver: AsyncMigration {
    func prepare(on database: any Database) async throws {
        try await database.schema("caregivers")
            .id()
            .field("name", .string, .required)
            .create()
    }

    func revert(on database: any Database) async throws {
        try await database.schema("caregivers").delete()
    }
}

struct CreateQuestion: AsyncMigration {
    func prepare(on database: any Database) async throws {
        try await database.schema("questions")
            .id()
            .field("question_text", .string, .required)
            .field("scoring_category", .string, .required)
            .field("options", .array(of: .string), .required)
            .unique(on: "question_text")
            .create()
    }

    func revert(on database: any Database) async throws {
        try await database.schema("questions").delete()
    }
}

struct CreateAnswerOption: AsyncMigration {
    func prepare(on database: any Database) async throws {
        try await database.schema("answer_options")
            .id()
            .field("question_text", .string, .required)
            .field("option_text", .string, .required)
            .field("score", .int, .required)
            .unique(on: "question_text", "option_text")
            .create()
    }

    func revert(on database: any Database) async throws {
        try await database.schema("answer_options").delete()
    }
}

struct CreateClientResponse: AsyncMigration {
    func prepare(on database: any Database) async throws {
        try await database.schema("client_responses")
            .id()
            .field("client_id", .uuid, .required, .references("clients", "id", onDelete: .cascade))
            .field("question_text", .string, .required)
            .field("selected_options", .array(of: .string), .required)
            .create()
    }

    func revert(on database: any Database) async throws {
        try await database.schema("client_responses").delete()
    }
}

struct CreateCaregiverResponse: AsyncMigration {
    func prepare(on database: any Database) async throws {
        try await database.schema("caregiver_responses")
            .id()
            .field("caregiver_id", .uuid, .required, .references("caregivers", "id", onDelete: .cascade))
            .field("question_text", .string, .required)
            .field("selected_options", .array(of: .string), .required)
            .create()
    }

    func revert(on database: any Database) async throws {
        try await database.schema("caregiver_responses").delete()
    }
}

struct CreateMatchResult: AsyncMigration {
    func prepare(on database: any Database) async throws {
        try await database.schema("match_results")
            .id()
            .field("client_id", .uuid, .required, .references("clients", "id", onDelete: .cascade))
            .field("caregiver_id", .uuid, .required, .references("caregivers", "id", onDelete: .cascade))
            .field("personality_communication_score", .double, .required)
            .field("care_needs_skills_score", .double, .required)
            .field("lifestyle_interests_score", .double, .required)
            .field("cultural_language_location_score", .double, .required)
            .field("logistics_schedule_score", .double, .required)
            .field("weighted_total_score", .double, .required)
            .field("match_percentage", .double, .required)
            .field("created_at", .datetime)
            .create()
    }

    func revert(on database: any Database) async throws {
        try await database.schema("match_results").delete()
    }
}

struct CreateMatchConfirmation: AsyncMigration {
    func prepare(on database: any Database) async throws {
        try await database.schema("match_confirmations")
            .id()
            .field("client_id", .uuid, .required, .references("clients", "id", onDelete: .cascade))
            .field("caregiver_id", .uuid, .required, .references("caregivers", "id", onDelete: .cascade))
            .field("match_result_id", .uuid, .required, .references("match_results", "id", onDelete: .cascade))
            .field("status", .string, .required)
            .field("confirmed_at", .datetime)
            .field("notes", .string)
            .unique(on: "client_id", "caregiver_id") // Prevent duplicate confirmations
            .create()
    }

    func revert(on database: any Database) async throws {
        try await database.schema("match_confirmations").delete()
    }
}
// MARK: - Seed Data Migration
struct SeedQuestionAndAnswerOptions: AsyncMigration {
    func prepare(on database: any Database) async throws {
        let questions: [Question] = [
            Question(questionText: "Preferred personality type", scoringCategory: ScoringCategory.personalityAndCommunication, options: ["Calm and quiet", "Outgoing and talkative", "Balanced and adaptable", "Energetic and enthusiastic", "Patient and reserved"]),
            Question(questionText: "Preferred communication style", scoringCategory: ScoringCategory.personalityAndCommunication, options: ["Direct and clear instructions", "Casual and conversational", "With patience and detailed explanations", "Using visual aids or demonstrations"]),
            Question(questionText: "Approach to care", scoringCategory: ScoringCategory.personalityAndCommunication, options: ["Takes initiative", "Follows instructions closely", "Balanced approach (proactive and responsive)"]),
            Question(questionText: "Top values in interaction", scoringCategory: ScoringCategory.personalityAndCommunication, options: ["Empathy", "Humor", "Patience", "Proactivity", "Flexibility", "Attention to detail"]),
            Question(questionText: "Comfort with emotional situations", scoringCategory: ScoringCategory.personalityAndCommunication, options: ["Remain calm and patient", "Use humor to ease tension", "Redirect focus to positive topics", "Encourage relaxation techniques", "Prefer not to engage deeply with emotional matters"]),

            Question(questionText: "Hobbies and activities", scoringCategory: ScoringCategory.lifestyleInterests, options: ["Gardening", "Reading", "Watching TV/Movies", "Cooking/Baking", "Board games or puzzles", "Walking/Outdoor activities", "Arts and crafts", "Music and singing", "Attending social events"]),
            Question(questionText: "Religious or spiritual importance", scoringCategory: ScoringCategory.lifestyleInterests, options: ["Very important", "Somewhat important", "Not important"]),
            Question(questionText: "Comfort with pets", scoringCategory: ScoringCategory.lifestyleInterests, options: ["Love pets", "Comfortable, with some allergies/preferences", "Prefer no pets"]),

            Question(questionText: "Certifications", scoringCategory: ScoringCategory.careNeedsAndSkills, options: ["CNA", "HHA", "CPR/First Aid", "Dementia Care", "None (willing to train)"]),
            Question(questionText: "Medical comfort level", scoringCategory: ScoringCategory.careNeedsAndSkills, options: ["Lifting/mobility transfers", "Cognitive decline/memory care", "Medical equipment (tubes, colostomy)", "Emotional companionship"]),

            Question(questionText: "Language preference", scoringCategory: ScoringCategory.culturalLanguageLocation, options: ["English", "Spanish", "French", "Other (Specify)"]),
            Question(questionText: "Location preference", scoringCategory: ScoringCategory.culturalLanguageLocation, options: ["Within 5 miles", "Within 10 miles", "Within 20 miles", "No preference"]),
            Question(questionText: "Cultural familiarity", scoringCategory: ScoringCategory.culturalLanguageLocation, options: ["Very familiar with multicultural care", "Somewhat familiar", "Not familiar, but open to learning"]),
            Question(questionText: "Dietary/cultural accommodations", scoringCategory: ScoringCategory.culturalLanguageLocation, options: ["Important, please specify", "No special accommodations needed"]),

            Question(questionText: "Preferred schedule", scoringCategory: ScoringCategory.logisticsAndSchedule, options: ["Full-time", "Part-time", "Overnight care", "On-call/flexible"])
        ]

        let answerOptions: [AnswerOption] = [
            // Preferred personality type
            AnswerOption(questionText: "Preferred personality type", optionText: "Calm and quiet", score: 5),
            AnswerOption(questionText: "Preferred personality type", optionText: "Outgoing and talkative", score: 3),
            AnswerOption(questionText: "Preferred personality type", optionText: "Balanced and adaptable", score: 4),
            AnswerOption(questionText: "Preferred personality type", optionText: "Energetic and enthusiastic", score: 2),
            AnswerOption(questionText: "Preferred personality type", optionText: "Patient and reserved", score: 5),

            // Preferred communication style
            AnswerOption(questionText: "Preferred communication style", optionText: "Direct and clear instructions", score: 5),
            AnswerOption(questionText: "Preferred communication style", optionText: "Casual and conversational", score: 4),
            AnswerOption(questionText: "Preferred communication style", optionText: "With patience and detailed explanations", score: 5),
            AnswerOption(questionText: "Preferred communication style", optionText: "Using visual aids or demonstrations", score: 4),

            // Approach to care
            AnswerOption(questionText: "Approach to care", optionText: "Takes initiative", score: 4),
            AnswerOption(questionText: "Approach to care", optionText: "Follows instructions closely", score: 4),
            AnswerOption(questionText: "Approach to care", optionText: "Balanced approach (proactive and responsive)", score: 5),

            // Religious or spiritual importance
            AnswerOption(questionText: "Religious or spiritual importance", optionText: "Very important", score: 5),
            AnswerOption(questionText: "Religious or spiritual importance", optionText: "Somewhat important", score: 3),
            AnswerOption(questionText: "Religious or spiritual importance", optionText: "Not important", score: 1),

            // Comfort with pets
            AnswerOption(questionText: "Comfort with pets", optionText: "Love pets", score: 5),
            AnswerOption(questionText: "Comfort with pets", optionText: "Comfortable, with some allergies/preferences", score: 3),
            AnswerOption(questionText: "Comfort with pets", optionText: "Prefer no pets", score: 1),

            // Cultural familiarity
            AnswerOption(questionText: "Cultural familiarity", optionText: "Very familiar with multicultural care", score: 5),
            AnswerOption(questionText: "Cultural familiarity", optionText: "Somewhat familiar", score: 3),
            AnswerOption(questionText: "Cultural familiarity", optionText: "Not familiar, but open to learning", score: 4),

            // Dietary/cultural accommodations
            AnswerOption(questionText: "Dietary/cultural accommodations", optionText: "Important, please specify", score: 5),
            AnswerOption(questionText: "Dietary/cultural accommodations", optionText: "No special accommodations needed", score: 3),

            // Preferred schedule
            AnswerOption(questionText: "Preferred schedule", optionText: "Full-time", score: 5),
            AnswerOption(questionText: "Preferred schedule", optionText: "Part-time", score: 3),
            AnswerOption(questionText: "Preferred schedule", optionText: "Overnight care", score: 4),
            AnswerOption(questionText: "Preferred schedule", optionText: "On-call/flexible", score: 4),

            // Top values in interaction - all scored equally
            AnswerOption(questionText: "Top values in interaction", optionText: "Empathy", score: 5),
            AnswerOption(questionText: "Top values in interaction", optionText: "Humor", score: 5),
            AnswerOption(questionText: "Top values in interaction", optionText: "Patience", score: 5),
            AnswerOption(questionText: "Top values in interaction", optionText: "Proactivity", score: 5),
            AnswerOption(questionText: "Top values in interaction", optionText: "Flexibility", score: 5),
            AnswerOption(questionText: "Top values in interaction", optionText: "Attention to detail", score: 5),

            // Comfort with emotional situations - all scored equally
            AnswerOption(questionText: "Comfort with emotional situations", optionText: "Remain calm and patient", score: 5),
            AnswerOption(questionText: "Comfort with emotional situations", optionText: "Use humor to ease tension", score: 5),
            AnswerOption(questionText: "Comfort with emotional situations", optionText: "Redirect focus to positive topics", score: 5),
            AnswerOption(questionText: "Comfort with emotional situations", optionText: "Encourage relaxation techniques", score: 5),
            AnswerOption(questionText: "Comfort with emotional situations", optionText: "Prefer not to engage deeply with emotional matters", score: 5),

            // Hobbies and activities - all scored equally
            AnswerOption(questionText: "Hobbies and activities", optionText: "Gardening", score: 5),
            AnswerOption(questionText: "Hobbies and activities", optionText: "Reading", score: 5),
            AnswerOption(questionText: "Hobbies and activities", optionText: "Watching TV/Movies", score: 5),
            AnswerOption(questionText: "Hobbies and activities", optionText: "Cooking/Baking", score: 5),
            AnswerOption(questionText: "Hobbies and activities", optionText: "Board games or puzzles", score: 5),
            AnswerOption(questionText: "Hobbies and activities", optionText: "Walking/Outdoor activities", score: 5),
            AnswerOption(questionText: "Hobbies and activities", optionText: "Arts and crafts", score: 5),
            AnswerOption(questionText: "Hobbies and activities", optionText: "Music and singing", score: 5),
            AnswerOption(questionText: "Hobbies and activities", optionText: "Attending social events", score: 5),

            // The rest of the options with default score of 5
            AnswerOption(questionText: "Language preference", optionText: "English", score: 5),
            AnswerOption(questionText: "Language preference", optionText: "Spanish", score: 5),
            AnswerOption(questionText: "Language preference", optionText: "French", score: 5),
            AnswerOption(questionText: "Language preference", optionText: "Other (Specify)", score: 5),

            AnswerOption(questionText: "Location preference", optionText: "Within 5 miles", score: 5),
            AnswerOption(questionText: "Location preference", optionText: "Within 10 miles", score: 5),
            AnswerOption(questionText: "Location preference", optionText: "Within 20 miles", score: 5),
            AnswerOption(questionText: "Location preference", optionText: "No preference", score: 5),

            AnswerOption(questionText: "Certifications", optionText: "CNA", score: 5),
            AnswerOption(questionText: "Certifications", optionText: "HHA", score: 5),
            AnswerOption(questionText: "Certifications", optionText: "CPR/First Aid", score: 5),
            AnswerOption(questionText: "Certifications", optionText: "Dementia Care", score: 5),
            AnswerOption(questionText: "Certifications", optionText: "None (willing to train)", score: 5),

            AnswerOption(questionText: "Medical comfort level", optionText: "Lifting/mobility transfers", score: 5),
            AnswerOption(questionText: "Medical comfort level", optionText: "Cognitive decline/memory care", score: 5),
            AnswerOption(questionText: "Medical comfort level", optionText: "Medical equipment (tubes, colostomy)", score: 5),
            AnswerOption(questionText: "Medical comfort level", optionText: "Emotional companionship", score: 5)
        ]

        // Save questions
        for question in questions {
            try await question.save(on: database)
        }

        // Save answer options
        for option in answerOptions {
            try await option.save(on: database)
        }
    }

    func revert(on database: any Database) async throws {
        try await AnswerOption.query(on: database).delete()
        try await Question.query(on: database).delete()
    }
}