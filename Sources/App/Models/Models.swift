//
//  Models.swift
//  MatchIQ
//
//  Created by <PERSON> on 4/21/25.
//

import Fluent
import Vapor

// MARK: - Constants

struct ScoringCategory {
    static let personalityAndCommunication = "Personality & Communication Match"
    static let careNeedsAndSkills = "Care Needs & Skills Alignment"
    static let lifestyleInterests = "Lifestyle Interests Match"
    static let culturalLanguageLocation = "Cultural/Language/Location Fit"
    static let logisticsAndSchedule = "Logistics & Schedule Compatibility"
}

struct CategoryWeight {
    static let personalityAndCommunication: Double = 0.4
    static let careNeedsAndSkills: Double = 0.25
    static let lifestyleInterests: Double = 0.15
    static let culturalLanguageLocation: Double = 0.1
    static let logisticsAndSchedule: Double = 0.1
}

// MARK: - Models

// User Model for Authentication and Role Management
final class User: Model, Content, @unchecked Sendable {
    static let schema = "users"

    @ID(key: .id)
    var id: UUID?

    @Field(key: "email")
    var email: String

    @Field(key: "phone")
    var phone: String?

    @Field(key: "password_hash")
    var passwordHash: String

    @Enum(key: "role")
    var role: UserRole

    @OptionalParent(key: "client_id")
    var client: Client?

    @OptionalParent(key: "caregiver_id")
    var caregiver: Caregiver?

    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?

    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?

    init() { }

    init(id: UUID? = nil, email: String, phone: String? = nil, passwordHash: String, role: UserRole) {
        self.id = id
        self.email = email
        self.phone = phone
        self.passwordHash = passwordHash
        self.role = role
    }
}

// User Role Enum
enum UserRole: String, Codable, CaseIterable {
    case client = "client"
    case caregiver = "caregiver"
    case staff = "staff"
}

// User Session Token for Authentication
final class UserToken: Model, Content, @unchecked Sendable {
    static let schema = "user_tokens"

    @ID(key: .id)
    var id: UUID?

    @Field(key: "value")
    var value: String

    @Parent(key: "user_id")
    var user: User

    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?

    @Timestamp(key: "expires_at", on: .none)
    var expiresAt: Date?

    init() { }

    init(id: UUID? = nil, value: String, userID: UUID, expiresAt: Date? = nil) {
        self.id = id
        self.value = value
        self.$user.id = userID
        self.expiresAt = expiresAt ?? Date().addingTimeInterval(60 * 60 * 24 * 30) // 30 days
    }
}

// Client Model
final class Client: Model, Content, @unchecked Sendable {
    static let schema = "clients"

    @ID(key: .id)
    var id: UUID?

    @Field(key: "name")
    var name: String

    @Field(key: "language")
    var language: String

    @OptionalField(key: "email")
    var email: String?

    @OptionalField(key: "phone")
    var phone: String?

    @OptionalField(key: "location")
    var location: String?

    @Children(for: \.$client)
    var responses: [ClientResponse]

    @OptionalChild(for: \.$client)
    var user: User?

    init() { }

    init(id: UUID? = nil, name: String, language: String = "en", email: String? = nil, phone: String? = nil, location: String? = nil) {
        self.id = id
        self.name = name
        self.language = language
        self.email = email
        self.phone = phone
        self.location = location
    }
}

// Caregiver Model
final class Caregiver: Model,Content, @unchecked Sendable {
    static let schema = "caregivers"

    @ID(key: .id)
    var id: UUID?

    @Field(key: "name")
    var name: String

    @Field(key: "language")
    var language: String

    @OptionalField(key: "email")
    var email: String?

    @OptionalField(key: "phone")
    var phone: String?

    @Children(for: \.$caregiver)
    var responses: [CaregiverResponse]

    @OptionalChild(for: \.$caregiver)
    var user: User?

    init() { }

    init(id: UUID? = nil, name: String, language: String = "en", email: String? = nil, phone: String? = nil) {
        self.id = id
        self.name = name
        self.language = language
        self.email = email
        self.phone = phone
    }
}

// Question Model
final class Question: Model, Content, @unchecked Sendable {
    static let schema = "questions"

    @ID(key: .id)
    var id: UUID?

    @Field(key: "question_text")
    var questionText: String

    @Field(key: "scoring_category")
    var scoringCategory: String

    @Field(key: "options")
    var options: [String]

    init() { }

    init(id: UUID? = nil, questionText: String, scoringCategory: String, options: [String]) {
        self.id = id
        self.questionText = questionText
        self.scoringCategory = scoringCategory
        self.options = options
    }
}

// Answer Option Model
final class AnswerOption:Model, Content, @unchecked Sendable {
    static let schema = "answer_options"

    @ID(key: .id)
    var id: UUID?

    @Field(key: "question_text")
    var questionText: String

    @Field(key: "option_text")
    var optionText: String

    @Field(key: "score")
    var score: Int

    init() { }

    init(id: UUID? = nil, questionText: String, optionText: String, score: Int) {
        self.id = id
        self.questionText = questionText
        self.optionText = optionText
        self.score = score
    }
}

// Client Response Model
final class ClientResponse: Model, @unchecked Sendable {
    static let schema = "client_responses"

    @ID(key: .id)
    var id: UUID?

    @Parent(key: "client_id")
    var client: Client

    @Field(key: "question_text")
    var questionText: String

    @Field(key: "selected_options")
    var selectedOptions: [String]

    init() { }

    init(id: UUID? = nil, clientID: UUID, questionText: String, selectedOptions: [String]) {
        self.id = id
        self.$client.id = clientID
        self.questionText = questionText
        self.selectedOptions = selectedOptions
    }
}

// Caregiver Response Model
final class CaregiverResponse: Model, @unchecked Sendable {
    static let schema = "caregiver_responses"

    @ID(key: .id)
    var id: UUID?

    @Parent(key: "caregiver_id")
    var caregiver: Caregiver

    @Field(key: "question_text")
    var questionText: String

    @Field(key: "selected_options")
    var selectedOptions: [String]

    init() { }

    init(id: UUID? = nil, caregiverID: UUID, questionText: String, selectedOptions: [String]) {
        self.id = id
        self.$caregiver.id = caregiverID
        self.questionText = questionText
        self.selectedOptions = selectedOptions
    }
}

// Match Result Model
final class MatchResult: Model, @unchecked Sendable {
    static let schema = "match_results"

    @ID(key: .id)
    var id: UUID?

    @Parent(key: "client_id")
    var client: Client

    @Parent(key: "caregiver_id")
    var caregiver: Caregiver

    @Field(key: "personality_communication_score")
    var personalityCommunicationScore: Double

    @Field(key: "care_needs_skills_score")
    var careNeedsSkillsScore: Double

    @Field(key: "lifestyle_interests_score")
    var lifestyleInterestsScore: Double

    @Field(key: "cultural_language_location_score")
    var culturalLanguageLocationScore: Double

    @Field(key: "logistics_schedule_score")
    var logisticsScheduleScore: Double

    @Field(key: "weighted_total_score")
    var weightedTotalScore: Double

    @Field(key: "match_percentage")
    var matchPercentage: Double

    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?

    init() { }

    init(
        id: UUID? = nil,
        clientID: UUID,
        caregiverID: UUID,
        personalityCommunicationScore: Double,
        careNeedsSkillsScore: Double,
        lifestyleInterestsScore: Double,
        culturalLanguageLocationScore: Double,
        logisticsScheduleScore: Double,
        weightedTotalScore: Double,
        matchPercentage: Double
    ) {
        self.id = id
        self.$client.id = clientID
        self.$caregiver.id = caregiverID
        self.personalityCommunicationScore = personalityCommunicationScore
        self.careNeedsSkillsScore = careNeedsSkillsScore
        self.lifestyleInterestsScore = lifestyleInterestsScore
        self.culturalLanguageLocationScore = culturalLanguageLocationScore
        self.logisticsScheduleScore = logisticsScheduleScore
        self.weightedTotalScore = weightedTotalScore
        self.matchPercentage = matchPercentage
    }
}

// Match Confirmation Model - tracks when clients confirm/reject matches
final class MatchConfirmation: Model, @unchecked Sendable {
    static let schema = "match_confirmations"

    @ID(key: .id)
    var id: UUID?

    @Parent(key: "client_id")
    var client: Client

    @Parent(key: "caregiver_id")
    var caregiver: Caregiver

    @Parent(key: "match_result_id")
    var matchResult: MatchResult

    @Field(key: "status")
    var status: ConfirmationStatus

    @Timestamp(key: "confirmed_at", on: .create)
    var confirmedAt: Date?

    @Field(key: "notes")
    var notes: String?

    // Staff validation fields
    @Field(key: "staff_validated")
    var staffValidated: Bool

    @OptionalParent(key: "validated_by_staff_id")
    var validatedByStaff: User?

    @Timestamp(key: "staff_validated_at", on: .none)
    var staffValidatedAt: Date?

    @Field(key: "staff_validation_notes")
    var staffValidationNotes: String?

    init() { }

    init(
        id: UUID? = nil,
        clientID: UUID,
        caregiverID: UUID,
        matchResultID: UUID,
        status: ConfirmationStatus,
        notes: String? = nil,
        staffValidated: Bool = false,
        staffValidationNotes: String? = nil
    ) {
        self.id = id
        self.$client.id = clientID
        self.$caregiver.id = caregiverID
        self.$matchResult.id = matchResultID
        self.status = status
        self.notes = notes
        self.staffValidated = staffValidated
        self.staffValidationNotes = staffValidationNotes
    }
}

enum ConfirmationStatus: String, Codable, CaseIterable {
    case confirmed = "confirmed"
    case rejected = "rejected"
    case pending = "pending"
}

// DTOs for form submission
struct ClientFormSubmission: Content {
    var name: String
    var language: String
    var email: String?
    var phone: String?
    var location: String?
    var responses: [QuestionResponse]

    struct QuestionResponse: Content {
        var questionText: String
        var selectedOptions: [String]

        // Custom decoding to handle single-string selectedOptions
        enum CodingKeys: String, CodingKey {
            case questionText
            case selectedOptions
        }

        init(from decoder: any Decoder) throws {
            let container = try decoder.container(keyedBy: CodingKeys.self)
            questionText = try container.decode(String.self, forKey: .questionText)

            // Decode selectedOptions as a single string and wrap it in an array
            let optionsString = try container.decode([String].self, forKey: .selectedOptions)
            selectedOptions = optionsString
        }
    }
}

struct CaregiverFormSubmission: Content {
    var name: String
    var language: String
    var email: String?
    var phone: String?
    var responses: [QuestionResponse]

    struct QuestionResponse: Content {
        var questionText: String
        var selectedOptions: [String]
    }
}

// DTO for match results
struct MatchSummary: Content {
    var caregiverId: UUID
    var caregiverName: String
    var matchPercentage: Double
    var categoryScores: [String: Double]
}

// DTO for client match results (for caregiver view)
struct ClientMatchSummary: Content {
    var clientId: UUID
    var clientName: String
    var matchPercentage: Double
    var categoryScores: [String: Double]
}
