//
//  File.swift
//  MatchIQ
//
//  Created by <PERSON> on 4/21/25.
//

import Fluent
import Vapor

struct FormData: Content {
    var name: String
    var responses: [Response]

    struct Response: Content {
        var selectedOptions: String
        var questionText: String
    }

    // Custom decoding to handle the array indices in the form data
    init(from decoder: any Decoder) throws {
        let container = try decoder.container(keyedBy: DynamicKey.self)

        // Decode the name field
        name = try container.decode(String.self, forKey: Dynamic<PERSON>ey(stringValue: "name"))

        // For response array, we need to detect all indices
        var responses: [Response] = []
        var index = 0

        // Keep trying indices until we don't find any more
        while true {
            let selectedOptionsKey = "responses[\(index)].selectedOptions"
            let questionTextKey = "responses[\(index)].questionText"

            guard container.contains(DynamicKey(stringValue: selectedOptionsKey)),
                  container.contains(DynamicKey(stringValue: questionTextKey)) else {
                break
            }

            let selectedOptions = try container.decode(String.self, forKey: Dynamic<PERSON>ey(stringValue: selectedOptionsKey))
            let questionText = try container.decode(String.self, forKey: DynamicKey(stringValue: questionTextKey))

            responses.append(Response(selectedOptions: selectedOptions, questionText: questionText))
            index += 1
        }

        self.responses = responses
    }
}

// Custom CodingKey to handle dynamic keys in the form data
struct DynamicKey: CodingKey {
    var stringValue: String
    var intValue: Int?

    init(stringValue: String) {
        self.stringValue = stringValue
        self.intValue = nil
    }

    init?(intValue: Int) {
        self.stringValue = "\(intValue)"
        self.intValue = intValue
    }
}

struct ClientController: RouteCollection {
    func boot(routes: any RoutesBuilder) throws {
        let clients = routes.grouped("clients")

        // Get all clients
        clients.get(use: self.index)

        // Get a specific client
        clients.get(":clientID", use: show)

        // Create a new client with form responses
        clients.post(use: create)

        // Delete a client
        clients.delete(":clientID", use: delete)

        // Get matches for a client
        clients.get(":clientID", "matches", use: getMatches)
    }

    // GET /clients
    @Sendable
    func index(req: Request) async throws ->[Client] {
        try await Client.query(on: req.db).all()
    }


    // GET /clients/:clientID
    @Sendable
    func show(req: Request) async throws -> Client {
        guard let clientID = req.parameters.get("clientID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid client ID")
        }

        guard let client = try await Client.find(clientID, on: req.db) else {
            throw Abort(.notFound, reason: "Client not found")
        }

        return client
    }

    struct QuestionnaireSubmission: Content {
        let name: String
        let responses: [Response]

        struct Response: Content {
            let question: String
            let selectedOptions: String
        }
    }

    // POST /clients
    @Sendable
    func create(req: Request) async throws -> Client {
        let formSubmission = try req.content.decode(ClientFormSubmission.self)

        // Check for duplicate name (case-insensitive)
        let normalizedName = formSubmission.name.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()

        // Check existing clients for duplicate name
        let existingClients = try await Client.query(on: req.db).all()
        for client in existingClients {
            if client.name.lowercased() == normalizedName {
                throw Abort(.badRequest, reason: "A client with the name '\(formSubmission.name)' is already registered in the system.")
            }
        }

        // Also check if a caregiver exists with the same name
        let existingCaregivers = try await Caregiver.query(on: req.db).all()
        for caregiver in existingCaregivers {
            if caregiver.name.lowercased() == normalizedName {
                throw Abort(.badRequest, reason: "A caregiver with the name '\(formSubmission.name)' is already registered in the system. Please use a different name or contact support.")
            }
        }

        // Check for duplicate email if provided
        if let email = formSubmission.email, !email.isEmpty {
            let normalizedEmail = email.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)

            // Check clients
            if let _ = try await Client.query(on: req.db)
                .filter(\.$email == normalizedEmail)
                .first() {
                throw Abort(.badRequest, reason: "A client account already exists with the email '\(email)'. Please use a different email or contact support if this is your account.")
            }

            // Check caregivers
            if let _ = try await Caregiver.query(on: req.db)
                .filter(\.$email == normalizedEmail)
                .first() {
                throw Abort(.badRequest, reason: "A caregiver account already exists with the email '\(email)'. Please use a different email or contact support if this is your account.")
            }
        }

        // Check for duplicate phone if provided
        if let phone = formSubmission.phone, !phone.isEmpty {
            let normalizedPhone = phone.trimmingCharacters(in: .whitespacesAndNewlines)

            // Check clients
            if let _ = try await Client.query(on: req.db)
                .filter(\.$phone == normalizedPhone)
                .first() {
                throw Abort(.badRequest, reason: "A client account already exists with the phone number '\(phone)'. Please use a different phone number or contact support if this is your account.")
            }

            // Check caregivers
            if let _ = try await Caregiver.query(on: req.db)
                .filter(\.$phone == normalizedPhone)
                .first() {
                throw Abort(.badRequest, reason: "A caregiver account already exists with the phone number '\(phone)'. Please use a different phone number or contact support if this is your account.")
            }
        }

        // Create client
        let client = Client(
            name: formSubmission.name,
            language: formSubmission.language,
            email: formSubmission.email?.lowercased(),
            phone: formSubmission.phone,
            location: formSubmission.location
        )
        try await client.save(on: req.db)

        // Save all responses
        for response in formSubmission.responses {
            let clientResponse = ClientResponse(
                clientID: client.id!,
                questionText: response.questionText,
                selectedOptions: response.selectedOptions
            )
            try await clientResponse.save(on: req.db)
        }

        return client
    }

    // DELETE /clients/:clientID
    @Sendable
    func delete(req: Request) async throws -> HTTPStatus {
        guard let clientID = req.parameters.get("clientID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid client ID")
        }

        guard let client = try await Client.find(clientID, on: req.db) else {
            throw Abort(.notFound, reason: "Client not found")
        }

        // Delete all client responses first (due to foreign key constraints)
        try await ClientResponse.query(on: req.db)
            .filter(\.$client.$id == clientID)
            .delete()

        // Delete the client
        try await client.delete(on: req.db)

        return .noContent
    }

    // GET /clients/:clientID/matches
    @Sendable
    func getMatches(req: Request) async throws -> [MatchSummary] {
        guard let clientID = req.parameters.get("clientID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid client ID")
        }

        guard let client = try await Client.find(clientID, on: req.db) else {
            throw Abort(.notFound, reason: "Client not found")
        }

        return try await MatchingService.findMatches(for: client, on: req.db)
    }
}
